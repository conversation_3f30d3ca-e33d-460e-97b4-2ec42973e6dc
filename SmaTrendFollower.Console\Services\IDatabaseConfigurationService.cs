using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for performance
/// </summary>
public interface IDatabaseConfigurationService
{
    /// <summary>
    /// Configures PostgreSQL DbContext options with optimized settings
    /// </summary>
    /// <param name="options">DbContext options builder</param>
    /// <param name="connectionString">PostgreSQL connection string</param>
    void ConfigurePostgreSQL(DbContextOptionsBuilder options, string connectionString);

    /// <summary>
    /// Gets optimized PostgreSQL connection string with performance settings
    /// </summary>
    /// <param name="baseConnectionString">Base PostgreSQL connection string</param>
    /// <returns>Optimized connection string</returns>
    string GetOptimizedPostgreSQLConnectionString(string baseConnectionString);

    /// <summary>
    /// Optimizes PostgreSQL database with VACUUM, ANALYZE, and other maintenance operations
    /// </summary>
    /// <param name="connectionString">PostgreSQL connection string</param>
    Task OptimizePostgreSQLAsync(string connectionString);

}

/// <summary>
/// Static helper methods for database configuration
/// </summary>
public static class DatabaseConfigurationHelper
{
    /// <summary>
    /// Creates an optimized PostgreSQL connection string builder with high-concurrency settings
    /// This is the standard configuration used throughout the trading system
    /// STANDARDIZED CONFIGURATION - Use this for all DbContext configurations
    /// </summary>
    /// <param name="baseConnectionString">Base connection string</param>
    /// <returns>Optimized connection string builder</returns>
    public static Npgsql.NpgsqlConnectionStringBuilder CreateOptimizedBuilder(string baseConnectionString)
    {
        return new Npgsql.NpgsqlConnectionStringBuilder(baseConnectionString)
        {
            // Connection Pool Settings - Conservative but sufficient for high-frequency trading
            MaxPoolSize = 50,  // Balanced: 3 contexts × 50 = 150 total (under PostgreSQL default 200)
            MinPoolSize = 10,  // Maintain warm connections for immediate availability
            Pooling = true,
            ConnectionIdleLifetime = 300, // 5 minutes - balance between resource usage and performance

            // Timeout Settings - Increased for high-load scenarios
            CommandTimeout = 60, // Increased from 45 to handle complex queries under load
            Timeout = 60,        // Increased from 45 to handle network delays and high load

            // Performance Optimizations
            Multiplexing = true,
            ReadBufferSize = 16384,  // Increased from 8192 for better throughput
            WriteBufferSize = 16384, // Increased from 8192 for better throughput

            // Network Resilience Settings - Critical for preventing stream read errors
            TcpKeepAlive = true,
            TcpKeepAliveTime = 30,     // More frequent keepalives (30s vs 60s) for faster detection
            TcpKeepAliveInterval = 5,  // Faster retry interval (5s vs 10s) for quicker recovery
            CancellationTimeout = 60000, // 60 seconds in milliseconds (increased from 45s)

            // Additional Resilience Settings
            IncludeErrorDetail = true,  // Better error diagnostics
            LogParameters = false       // Disable for production performance
        };
    }
}
